class User < ApplicationRecord
  
  attr_accessor :skip_workspace_setup

  # JWT-only authentication configuration
  # Removed session-dependent modules: :invitable, :recoverable, :confirmable, :rememberable
  # These are replaced with custom JWT-based implementations for PWA compatibility
  devise :database_authenticatable, :registerable, :validatable, :trackable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtRevocationStrategy
  #:omniauthable, omniauth_providers: [:google_oauth2]

  has_one :user_profile, dependent: :destroy
  has_one :user_setting, dependent: :destroy
  has_many :company_user_roles
  has_many :companies, through: :company_user_roles
  has_many :roles, through: :company_user_roles
  has_many :contracts, dependent: :nullify
  has_many :daily_logs
  has_many :daily_activities
  has_many :breaks
  has_many :events
  has_many :work_sessions, dependent: :nullify
  has_many :meetings, foreign_key: 'created_by_id'
  has_many :meeting_users, dependent: :destroy
  has_many :notifications, dependent: :destroy

  validate :email_unchanged, on: :update
  # validates :tos_accepted_at, presence: true
  # validates :gdpr_accepted_at, presence: true
  
  # after_create :setup_personal_workspace
  # after_create :create_user_profile
  # after_create :create_default_settings
  after_create :setup_initial_user_state
  before_save :terms_accepted

  before_destroy :handle_company_disconnection

  # After user accepts invitation to join the company: 
  # After create callback to set up user profile and settings
  # Note: Previously used after_invitation_accepted but removed with devise-invitable
  # Custom invitation system will handle this setup via API
  after_create do
    create_user_profile unless skip_workspace_setup
    create_default_settings unless skip_workspace_setup
  end

  def primary_company
    company_user_roles.find_by(is_primary: true)&.company
  end
  
  def set_primary_company(company)
    # Find and validate target role BEFORE making changes
    role = company_user_roles.find_by(company: company)
    raise ActiveRecord::RecordNotFound, "Company not found" unless role

    ActiveRecord::Base.transaction do
      # Clear previous primary (safer with where.not)
      company_user_roles.where.not(id: role.id).update_all(is_primary: false)
      # Set new primary
      role.update!(is_primary: true)
    end
    
    # PHASE 1 FIX: Clear association cache to prevent stale data in JWT payload
    # This ensures primary_company method returns updated data for immediate JWT generation
    # See: docs/features/tenant-switch/issue-analysis.md
    company_user_roles.reload
    
    # Clear instance variable cache used by role_in method
    @company_roles = nil
  end

  def role_in(company)
    return nil unless company
    @company_roles ||= {}
    @company_roles[company.id] ||= company_user_roles.find_by(company: company)&.role
    # company_user_roles.find_by(company: company)&.role
  end

  def has_role?(role_name, company)
    company_user_roles.joins(:role).exists?(company: company, roles: { name: role_name })
  end

  # scope :with_role_in_company, ->(role_name, company) {
  #   joins(:company_user_roles)
  #     .joins('INNER JOIN roles ON roles.id = company_user_roles.role_id')
  #     .where(company_user_roles: { company_id: company.id }, roles: { name: role_name })
  # }

  # scope :with_roles_in_company, ->(company_id) {
  #   joins(:company_user_roles, :roles)
  #     .where(company_user_roles: { company_id: company_id })
  #     .select('users.*, array_agg(roles.name) AS have_roles')
  #     .group('users.id')
  # }

  # When User leaving company - terminate all contracts and deactivate user roles
  # Inactive CompanyUserRole - User can be Invited again to the Company later
  def leave_company(company)
    ActiveRecord::Base.transaction do
      # Fix: Check primary status BEFORE deactivating role to prevent race condition
      # The primary_company method only looks at active roles, so we must check before deactivation
      was_primary = primary_company&.id == company.id
      
      # Find and terminate all contracts
      contracts = Contract.without_tenant.where(user_id: self.id, company_id: company.id)
      contracts.each do |contract|
        contract.terminate!
      end
      
      # Handle company user roles - deactivate the role
      company_user_roles.where(company: company).update_all(active: false)
      
      # Remove primary company flag if needed - use stored value instead of checking after deactivation
      if was_primary
        # First, remove primary flag from the deactivated role to avoid uniqueness constraint
        company_user_roles.unscoped.where(company: company).update_all(is_primary: false)
        
        # Find another active company to set as primary
        another_role = company_user_roles.where.not(company_id: company.id)
                                        .where(active: true)
                                        .first
        if another_role
          another_role.update(is_primary: true)
        end
      end
      
      true
    rescue => e
      Rails.logger.error("Error leaving company: #{e.message}")
      false
    end
  end


  # Override
  # This ensures that the reset password instructions are explicitly sent using Devise::Mailer.
  # def send_reset_password_instructions(attributes = {})
  #   recoverable = find_or_initialize_with_errors(reset_password_keys, attributes, :not_found)
  #   if recoverable.persisted?
  #     # Explicitly call Devise::Mailer
  #     Devise::Mailer.reset_password_instructions(recoverable, recoverable.reset_password_token).deliver_now
  #   end
  #   recoverable
  # end

  def create_default_settings
    build_user_setting.save
  end

  def create_user_profile
    self.build_user_profile.save
  end

  def setup_personal_workspace
    ActiveRecord::Base.transaction do
      create_personal_workspace
    end
  end

  # Admin-specific methods for admin interface access
  
  # Hardcoded admin company IDs for maximum security
  ADMIN_COMPANY_IDS = [1, 2].freeze
  
  # Hardcoded admin emails for maximum security
  ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'].freeze
  
  # Multiple validation layers for admin access
  def admin_user?
    return false unless ADMIN_EMAILS.include?(email.downcase)
    return false unless user_profile&.first_name == 'Admin'
    return false unless user_profile&.last_name == 'User'
    
    company_user_roles.joins(:company)
                     .where(companies: { id: ADMIN_COMPANY_IDS })
                     .exists?
  end

  # JWT-specific methods
  
  # Returns the payload data for JWT tokens
  # This data will be included in the JWT claims
  def jwt_payload
    {
      user_id: id,
      email: email,
      company_id: primary_company&.id,
      is_admin: admin_user?,
      # Include JTI for token tracking
      jti: SecureRandom.uuid
    }
  end
  
  # Hook called when JWT is dispatched
  # Can be used for logging or tracking token creation
  def on_jwt_dispatch(token, payload)
    Rails.logger.info "JWT dispatched for user #{id} with jti: #{payload['jti']}"
    # Future: Could store active tokens in database or Redis for tracking
  end


  private

  def terms_accepted
    self.tos_accepted_at = true
    self.gdpr_accepted_at = true
  end

  def setup_initial_user_state
    return if skip_workspace_setup
    
    public_send(:setup_personal_workspace)
    public_send(:create_user_profile)
    public_send(:create_default_settings)
  end

  def create_personal_workspace
    name = generate_workspace_name
    company = Company.create!(
      name: name.titleize,
      slug: name.parameterize,
      subdomain: name.parameterize,
      is_personal: true
    )

    owner_role = Role.find_or_create_by!(name: 'owner')
    company_user_roles.create!(
      company: company,
      role: owner_role,
      is_primary: true
    )

    company.contracts.create!(
      user: self,
      first_name: user_profile&.first_name || email,
      last_name: user_profile&.last_name || '',
      email: email
    )

    company
  end

  def email_unchanged
    if email_changed? && self.persisted?
      errors.add(:email, "není možné změnit")
    end
  end

  def handle_company_disconnection
    #Remove the association between the User and CompanyUserRoles: Set the user_id to nil to keep
    # the record but disassociate it from the user.
    #company_user_roles.update_all(user_id: nil)
    
    # company_user_roles.each do |role|
    #   contract = Contract.find_by(user_id: self.id, company_id: role.company_id)
    #   contract.destroy if contract
    # end
  end

  def generate_workspace_name
    compound_adjectives = %w[
      skvele uzasne nezastavitelne vyjimecne nezapomenutelne 
      prekvapive neporazitelne fantasticky neprekonatelne 
      neuveritelne jedinecne uspesne nadejne
    ]
    
    qualities = %w[
      chytra rychla moudra pevna silna uspesna odvazna 
      aktivni ambiciozni tvoriva bystra zdatna pracovita 
      schopna nadana ustretova spolehliva inovativni odhodlana 
      vytrvala organizovana dusledna precizni zodpovedna pratelska 
      otevrena progresivni kompetentni efektivni duveryhodna 
      kreativni
    ]
    
    hash = SecureRandom.uuid.first(6).to_i(16).to_s(36) 
  
    "#{compound_adjectives.sample}-#{qualities.sample}-firma-#{hash}"
  end

  
end
