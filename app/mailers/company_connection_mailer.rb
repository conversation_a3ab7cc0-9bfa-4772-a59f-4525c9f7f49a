class CompanyConnectionMailer < ApplicationMailer
  
  def existing_user_notification(sender:, user:, company:, contract:)
    @sender = sender.email
    @user = user
    @company = company
    @contract = contract
    subject = t('mailer.existing_user_notification.subject', default: 'Nové pozvání na Týmboxu')
    from = t('mailer.existing_user_notification.from', default: 'Aplikace Týmbox <<EMAIL>>')
    
    mail(
      to: user.email,
      subject: "#{subject} - #{company.name}",
      from: "#{from}"
    )
  end

  # Unified invitation mailer supporting JWT tokens with Czech templates
  def new_user_invitation(sender:, email:, company:, contract:, token:)
    @sender = sender.respond_to?(:email) ? sender.email : sender
    @company = company
    @contract = contract
    @token = token
    
    # Generate JWT invitation URL (matching JWT mailer pattern)
    @invitation_url = build_jwt_invitation_url(token, I18n.locale)
    
    subject = t('mailer.new_user_invitation.subject', default: 'Pozvánka do Týmboxu od')
    from = t('mailer.new_user_invitation.from', default: 'Aplikace Týmbox <<EMAIL>>')
    
    # Log email sending for security audit (matching JWT mailer pattern)
    Rails.logger.info "[SECURITY] Sending new user invitation email to #{email} for company #{company.id} (Sender: #{@sender}) in locale: #{I18n.locale}"
    
    mail(
      to: email,
      subject: "#{subject} #{company.name}",
      from: "#{from}"
    )
  end

  private

  def build_jwt_invitation_url(invitation_token, locale)
    # Build URL to the SPA invitation acceptance page with the token and locale
    # (copied from JwtInvitationMailer to maintain exact same URL structure)
    path = 'auth/accept-invitation'
    
    if Rails.env.development?
      "http://************:5100/#{locale}/#{path}?token=#{invitation_token}"
    else
      # Production URL - adjust based on your domain
      "https://#{Rails.application.config.action_mailer.default_url_options[:host]}/#{locale}/#{path}?token=#{invitation_token}"
    end
  end

end