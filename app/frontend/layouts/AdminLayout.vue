<template>
  <div class="admin-container" data-testid="admin-layout">
    <AdminSidebar />
    <div class="admin-main-content">
      <AdminTopbar />
      <div class="admin-content-area">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import AdminTopbar from '../components/admin/AdminTopbar.vue'
import AdminSidebar from '../components/admin/AdminSidebar.vue'
import authorizationMixin from '../mixins/authorizationMixin'

export default {
  name: 'AdminLayout',
  components: {
    AdminTopbar,
    AdminSidebar
  },
  mixins: [authorizationMixin],
  created() {
    // Verify admin access on component creation
    if (!this.isAdminUser()) {
      this.$router.push(`/${this.$route.params.locale}/dashboard`)
    }
  },
  methods: {
    isAdminUser() {
      const userData = this.$store.state.userStore.jwtUser || this.$store.state.userStore.user
      return userData?.is_admin || false
    }
  }
}
</script>

<style scoped>
.admin-container {
  @apply h-screen flex bg-gray-50;
}

.admin-main-content {
  @apply flex-1 flex flex-col overflow-hidden;
}

.admin-content-area {
  @apply flex-1 overflow-auto p-6;
}

/* Admin-specific styling */
.admin-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.admin-main-content {
  background: #f8fafc;
  margin-left: 16rem;
  border-radius: 1rem 0 0 1rem;
}

@media (max-width: 1024px) {
  .admin-main-content {
    margin-left: 0;
    border-radius: 0;
  }
}
</style>