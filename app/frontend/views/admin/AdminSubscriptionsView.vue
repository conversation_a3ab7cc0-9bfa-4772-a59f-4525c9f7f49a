<!-- ABOUTME: AdminSubscriptionsView displays and manages subscription information for admin users -->
<!-- ABOUTME: Provides detailed subscription listing with plan information and billing status (Phase 2 feature) -->
<template>
  <div class="admin-subscriptions" data-testid="admin-subscriptions">
    <div class="page-header mb-8">
      <h1 class="page-title">{{ $t('admin.subscriptions.title', 'Subscription Management') }}</h1>
      <p class="page-subtitle">{{ $t('admin.subscriptions.subtitle', 'Manage subscription plans and billing') }}</p>
    </div>
    
    <div class="coming-soon-state" data-testid="coming-soon-state">
      <div class="coming-soon-icon">🚧</div>
      <h3>{{ $t('admin.subscriptions.coming_soon', 'Coming Soon') }}</h3>
      <p>{{ $t('admin.subscriptions.phase_2_feature', 'Subscription management will be available in Phase 2') }}</p>
      
      <div class="planned-features">
        <h4>{{ $t('admin.subscriptions.planned_features', 'Planned Features:') }}</h4>
        <ul>
          <li>{{ $t('admin.subscriptions.feature_1', 'View all subscription plans and pricing') }}</li>
          <li>{{ $t('admin.subscriptions.feature_2', 'Manage subscription status and billing') }}</li>
          <li>{{ $t('admin.subscriptions.feature_3', 'Subscription analytics and revenue tracking') }}</li>
          <li>{{ $t('admin.subscriptions.feature_4', 'Create and modify subscription plans') }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminSubscriptionsView'
}
</script>

<style scoped>
.admin-subscriptions {
  @apply p-6;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600 text-lg;
}

.coming-soon-state {
  @apply text-center p-12 bg-white rounded-lg shadow-sm border border-gray-200;
}

.coming-soon-icon {
  @apply text-6xl mb-6;
}

.coming-soon-state h3 {
  @apply text-2xl font-bold text-gray-900 mb-4;
}

.coming-soon-state p {
  @apply text-gray-600 mb-8;
}

.planned-features {
  @apply text-left max-w-md mx-auto;
}

.planned-features h4 {
  @apply font-semibold text-gray-900 mb-4;
}

.planned-features ul {
  @apply list-disc list-inside space-y-2 text-gray-700;
}
</style>