<!-- ABOUTME: AdminCompaniesView displays and manages company information for admin users -->
<!-- ABOUTME: Provides detailed company listing with owner contacts, employee counts, and subscription status -->
<template>
  <div class="admin-companies" data-testid="admin-companies">
    <div class="page-header mb-8">
      <h1 class="page-title">{{ $t('admin.companies.title', 'Company Management') }}</h1>
      <p class="page-subtitle">{{ $t('admin.companies.subtitle', 'Manage companies, owners, and subscriptions') }}</p>
    </div>
    
    <!-- Search and Filters -->
    <div class="filters-section mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="search-box flex-1">
          <input
            v-model="searchTerm"
            type="text"
            :placeholder="$t('admin.companies.search_placeholder', 'Search companies...')"
            class="search-input"
            data-testid="company-search"
          />
        </div>
        <div class="filter-controls flex gap-2">
          <select v-model="planFilter" class="filter-select" data-testid="plan-filter">
            <option value="">{{ $t('admin.companies.all_plans', 'All Plans') }}</option>
            <option value="free">Free</option>
            <option value="plus">Plus</option>
            <option value="premium">Premium</option>
          </select>
          <button @click="refreshCompanies" class="refresh-btn" :disabled="isLoading">
            <i class="refresh-icon" :class="{ 'spinning': isLoading }">🔄</i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Companies Table -->
    <div class="companies-table-container">
      <div v-if="isLoading" class="loading-state" data-testid="loading-state">
        <div class="loading-spinner"></div>
        <p>{{ $t('admin.companies.loading', 'Loading companies...') }}</p>
      </div>
      
      <div v-else-if="hasError" class="error-state" data-testid="error-state">
        <div class="error-icon">⚠️</div>
        <h3>{{ $t('admin.companies.error_title', 'Failed to load companies') }}</h3>
        <p>{{ errorMessage }}</p>
        <button @click="loadCompanies" class="retry-button">
          {{ $t('admin.companies.retry', 'Retry') }}
        </button>
      </div>
      
      <div v-else-if="filteredCompanies.length === 0" class="empty-state" data-testid="empty-state">
        <div class="empty-icon">🏢</div>
        <h3>{{ $t('admin.companies.no_companies', 'No companies found') }}</h3>
        <p>{{ searchTerm ? $t('admin.companies.no_search_results', 'No companies match your search') : $t('admin.companies.no_companies_message', 'No companies registered yet') }}</p>
      </div>
      
      <div v-else class="table-wrapper">
        <table class="companies-table" data-testid="companies-table">
          <thead>
            <tr>
              <th>{{ $t('admin.companies.company_name', 'Company Name') }}</th>
              <th>{{ $t('admin.companies.owner', 'Owner') }}</th>
              <th>{{ $t('admin.companies.employees', 'Employees') }}</th>
              <th>{{ $t('admin.companies.plan', 'Plan') }}</th>
              <th>{{ $t('admin.companies.status', 'Status') }}</th>
              <th>{{ $t('admin.companies.created', 'Created') }}</th>
              <th>{{ $t('admin.companies.actions', 'Actions') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="company in filteredCompanies" :key="company.id" class="table-row" :data-testid="`company-row-${company.id}`">
              <td class="company-name-cell">
                <div class="company-info">
                  <div class="company-avatar">
                    {{ company.name.substring(0, 2).toUpperCase() }}
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ company.name }}</div>
                    <div class="text-sm text-gray-500">ID: {{ company.id }}</div>
                  </div>
                </div>
              </td>
              <td class="owner-cell">
                <div v-if="company.owner">
                  <div class="font-medium text-gray-900">{{ company.owner.name || 'N/A' }}</div>
                  <div class="text-sm text-gray-500">{{ company.owner.email }}</div>
                </div>
                <div v-else class="text-gray-400">{{ $t('admin.companies.no_owner', 'No owner') }}</div>
              </td>
              <td class="employee-count">
                <span class="employee-badge">{{ company.employee_count || 0 }}</span>
              </td>
              <td class="plan-cell">
                <span class="plan-badge" :class="getPlanBadgeClass(company.plan?.name)">
                  {{ formatPlanName(company.plan?.name) }}
                </span>
              </td>
              <td class="status-cell">
                <span class="status-badge" :class="getStatusBadgeClass(company.subscription?.status)">
                  {{ formatStatus(company.subscription?.status) }}
                </span>
              </td>
              <td class="created-date">
                {{ formatDate(company.created_at) }}
              </td>
              <td class="actions-cell">
                <div class="action-buttons">
                  <button 
                    @click="viewCompanyDetails(company)"
                    class="action-btn view-btn"
                    :title="$t('admin.companies.view_details', 'View Details')"
                  >
                    👁️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Company Details Modal -->
    <div v-if="selectedCompany" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop data-testid="company-details-modal">
        <div class="modal-header">
          <h3 class="modal-title">{{ $t('admin.companies.company_details', 'Company Details') }}</h3>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-grid">
            <div class="detail-item">
              <label>{{ $t('admin.companies.company_name', 'Company Name') }}</label>
              <span>{{ selectedCompany.name }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.company_id', 'Company ID') }}</label>
              <span>{{ selectedCompany.id }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.owner_email', 'Owner Email') }}</label>
              <span>{{ selectedCompany.owner?.email || 'N/A' }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.owner_name', 'Owner Name') }}</label>
              <span>{{ selectedCompany.owner?.name || 'N/A' }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.employee_count', 'Employee Count') }}</label>
              <span>{{ selectedCompany.employee_count || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.current_plan', 'Current Plan') }}</label>
              <span>{{ formatPlanName(selectedCompany.plan?.name) }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.subscription_status', 'Subscription Status') }}</label>
              <span>{{ formatStatus(selectedCompany.subscription?.status) }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('admin.companies.created_at', 'Created At') }}</label>
              <span>{{ formatDate(selectedCompany.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { sendFlashMessage } from '@/utils/flashMessage'

export default {
  name: 'AdminCompaniesView',
  data() {
    return {
      companies: [],
      isLoading: false,
      hasError: false,
      errorMessage: '',
      searchTerm: '',
      planFilter: '',
      selectedCompany: null
    }
  },
  computed: {
    filteredCompanies() {
      return this.companies.filter(company => {
        const matchesSearch = !this.searchTerm || 
          company.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          company.owner?.email?.toLowerCase().includes(this.searchTerm.toLowerCase())
        
        const matchesPlan = !this.planFilter || 
          (company.plan?.name || 'free').toLowerCase() === this.planFilter.toLowerCase()
        
        return matchesSearch && matchesPlan
      })
    }
  },
  async created() {
    await this.loadCompanies()
  },
  methods: {
    async loadCompanies() {
      this.isLoading = true
      this.hasError = false
      
      try {
        const response = await axios.get('/api/v1/admin/companies', {
          headers: { 'Accept': 'application/json' }
        })
        
        this.companies = response.data.companies || []
      } catch (error) {
        console.error('Failed to load companies:', error)
        this.hasError = true
        this.errorMessage = error.response?.data?.message || 'Failed to load companies'
        sendFlashMessage('Failed to load companies', 'error')
      } finally {
        this.isLoading = false
      }
    },
    
    async refreshCompanies() {
      await this.loadCompanies()
    },
    
    viewCompanyDetails(company) {
      this.selectedCompany = company
    },
    
    closeModal() {
      this.selectedCompany = null
    },
    
    formatPlanName(planName) {
      if (!planName) return 'Free'
      return planName.charAt(0).toUpperCase() + planName.slice(1)
    },
    
    formatStatus(status) {
      if (!status) return 'Unknown'
      return status.charAt(0).toUpperCase() + status.slice(1)
    },
    
    formatDate(dateString) {
      if (!dateString) return 'N/A'
      return new Date(dateString).toLocaleDateString()
    },
    
    getPlanBadgeClass(planName) {
      const plan = (planName || 'free').toLowerCase()
      return {
        'plan-free': plan === 'free',
        'plan-plus': plan === 'plus',
        'plan-premium': plan === 'premium'
      }
    },
    
    getStatusBadgeClass(status) {
      const statusLower = (status || 'unknown').toLowerCase()
      return {
        'status-active': statusLower === 'active',
        'status-trialing': statusLower === 'trialing',
        'status-canceled': statusLower === 'canceled',
        'status-unknown': statusLower === 'unknown'
      }
    }
  }
}
</script>

<style scoped>
.admin-companies {
  @apply p-6;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600 text-lg;
}

.filters-section {
  @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
}

.search-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent;
}

.refresh-btn {
  @apply px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors duration-200 disabled:opacity-50;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

.companies-table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.table-wrapper {
  @apply overflow-x-auto;
}

.companies-table {
  @apply w-full;
}

.companies-table th {
  @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.companies-table td {
  @apply px-6 py-4 whitespace-nowrap border-b border-gray-200;
}

.company-info {
  @apply flex items-center space-x-3;
}

.company-avatar {
  @apply w-10 h-10 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold;
}

.employee-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

.plan-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.plan-free {
  @apply bg-gray-100 text-gray-800;
}

.plan-plus {
  @apply bg-blue-100 text-blue-800;
}

.plan-premium {
  @apply bg-purple-100 text-purple-800;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-green-100 text-green-800;
}

.status-trialing {
  @apply bg-yellow-100 text-yellow-800;
}

.status-canceled {
  @apply bg-red-100 text-red-800;
}

.status-unknown {
  @apply bg-gray-100 text-gray-800;
}

.action-buttons {
  @apply flex space-x-2;
}

.action-btn {
  @apply p-2 rounded hover:bg-gray-100 transition-colors duration-200;
}

.loading-state,
.error-state,
.empty-state {
  @apply text-center p-8;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4;
}

.error-icon,
.empty-icon {
  @apply text-4xl mb-4;
}

.retry-button {
  @apply mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors duration-200;
}

.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-96 overflow-y-auto;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-xl font-semibold text-gray-900;
}

.close-btn {
  @apply text-2xl text-gray-400 hover:text-gray-600 transition-colors duration-200;
}

.modal-body {
  @apply p-6;
}

.detail-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.detail-item {
  @apply flex flex-col;
}

.detail-item label {
  @apply text-sm font-medium text-gray-500 mb-1;
}

.detail-item span {
  @apply text-sm text-gray-900;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>