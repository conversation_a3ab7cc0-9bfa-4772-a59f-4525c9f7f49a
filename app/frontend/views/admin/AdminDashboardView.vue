<!-- ABOUTME: AdminDashboardView displays overview statistics and metrics for admin interface -->
<!-- ABOUTME: Provides quick access to key system information and navigation to detailed admin sections -->
<template>
  <div class="admin-dashboard" data-testid="admin-dashboard">
    <div class="dashboard-header mb-8">
      <h1 class="dashboard-title">{{ $t('admin.dashboard.title', 'Admin Dashboard') }}</h1>
      <p class="dashboard-subtitle">{{ $t('admin.dashboard.subtitle', 'System overview and management') }}</p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="stats-grid mb-8">
      <div class="stat-card" data-testid="total-companies-card">
        <div class="stat-header">
          <h3 class="stat-title">{{ $t('admin.dashboard.total_companies', 'Total Companies') }}</h3>
          <div class="stat-icon companies">🏢</div>
        </div>
        <div class="stat-value">{{ dashboardStats.total_companies || 0 }}</div>
        <div class="stat-change positive">{{ $t('admin.dashboard.active', 'Active') }}</div>
      </div>
      
      <div class="stat-card" data-testid="active-subscriptions-card">
        <div class="stat-header">
          <h3 class="stat-title">{{ $t('admin.dashboard.active_subscriptions', 'Active Subscriptions') }}</h3>
          <div class="stat-icon subscriptions">💳</div>
        </div>
        <div class="stat-value">{{ dashboardStats.active_subscriptions || 0 }}</div>
        <div class="stat-change positive">{{ $t('admin.dashboard.paying_customers', 'Paying customers') }}</div>
      </div>
      
      <div class="stat-card" data-testid="trial-subscriptions-card">
        <div class="stat-header">
          <h3 class="stat-title">{{ $t('admin.dashboard.trial_subscriptions', 'Trial Subscriptions') }}</h3>
          <div class="stat-icon trials">🔄</div>
        </div>
        <div class="stat-value">{{ dashboardStats.trial_subscriptions || 0 }}</div>
        <div class="stat-change neutral">{{ $t('admin.dashboard.trial_period', 'Trial period') }}</div>
      </div>
      
      <div class="stat-card" data-testid="total-users-card">
        <div class="stat-header">
          <h3 class="stat-title">{{ $t('admin.dashboard.total_users', 'Total Users') }}</h3>
          <div class="stat-icon users">👥</div>
        </div>
        <div class="stat-value">{{ dashboardStats.total_users || 0 }}</div>
        <div class="stat-change positive">{{ $t('admin.dashboard.registered', 'Registered') }}</div>
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions mb-8">
      <h2 class="section-title">{{ $t('admin.dashboard.quick_actions', 'Quick Actions') }}</h2>
      <div class="actions-grid">
        <router-link 
          :to="`/${$route.params.locale}/admin/companies`"
          class="action-card"
          data-testid="manage-companies-action"
        >
          <div class="action-icon">🏢</div>
          <div class="action-content">
            <h3 class="action-title">{{ $t('admin.dashboard.manage_companies', 'Manage Companies') }}</h3>
            <p class="action-description">{{ $t('admin.dashboard.view_company_details', 'View company details, owners, and employees') }}</p>
          </div>
        </router-link>
        
        <router-link 
          :to="`/${$route.params.locale}/admin/subscriptions`"
          class="action-card"
          data-testid="manage-subscriptions-action"
        >
          <div class="action-icon">💳</div>
          <div class="action-content">
            <h3 class="action-title">{{ $t('admin.dashboard.manage_subscriptions', 'Manage Subscriptions') }}</h3>
            <p class="action-description">{{ $t('admin.dashboard.view_subscription_details', 'View and manage subscription plans and billing') }}</p>
          </div>
        </router-link>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state" data-testid="loading-state">
      <div class="loading-spinner"></div>
      <p>{{ $t('admin.dashboard.loading', 'Loading dashboard data...') }}</p>
    </div>
    
    <!-- Error State -->
    <div v-if="hasError" class="error-state" data-testid="error-state">
      <div class="error-icon">⚠️</div>
      <h3>{{ $t('admin.dashboard.error_title', 'Failed to load dashboard') }}</h3>
      <p>{{ errorMessage }}</p>
      <button @click="loadDashboardStats" class="retry-button">
        {{ $t('admin.dashboard.retry', 'Retry') }}
      </button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { sendFlashMessage } from '@/utils/flashMessage'

export default {
  name: 'AdminDashboardView',
  data() {
    return {
      dashboardStats: {},
      isLoading: false,
      hasError: false,
      errorMessage: ''
    }
  },
  async created() {
    await this.loadDashboardStats()
  },
  methods: {
    async loadDashboardStats() {
      this.isLoading = true
      this.hasError = false
      
      try {
        const response = await axios.get('/api/v1/admin/dashboard', {
          headers: { 'Accept': 'application/json' }
        })
        
        this.dashboardStats = response.data.dashboard || {}
      } catch (error) {
        console.error('Failed to load dashboard stats:', error)
        this.hasError = true
        this.errorMessage = error.response?.data?.message || 'Failed to load dashboard data'
        sendFlashMessage('Failed to load dashboard data', 'error')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  @apply p-6;
}

.dashboard-header {
  @apply text-center;
}

.dashboard-title {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.dashboard-subtitle {
  @apply text-gray-600 text-lg;
}

.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
}

.stat-header {
  @apply flex items-center justify-between mb-4;
}

.stat-title {
  @apply text-sm font-medium text-gray-600;
}

.stat-icon {
  @apply text-2xl;
}

.stat-value {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.stat-change {
  @apply text-sm font-medium;
}

.stat-change.positive {
  @apply text-green-600;
}

.stat-change.neutral {
  @apply text-gray-500;
}

.section-title {
  @apply text-xl font-semibold text-gray-900 mb-4;
}

.actions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.action-card {
  @apply bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md hover:border-purple-300 transition-all duration-200 flex items-start space-x-4;
  text-decoration: none;
}

.action-icon {
  @apply text-3xl flex-shrink-0;
}

.action-content {
  @apply flex-1;
}

.action-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.action-description {
  @apply text-gray-600;
}

.loading-state,
.error-state {
  @apply text-center p-8;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4;
}

.error-icon {
  @apply text-4xl mb-4;
}

.retry-button {
  @apply mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors duration-200;
}
</style>