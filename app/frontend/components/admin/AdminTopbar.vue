<!-- ABOUTME: AdminTopbar provides top navigation and actions for admin interface -->
<!-- ABOUTME: Displays current admin context and quick action buttons -->
<template>
  <div class="admin-topbar" data-testid="admin-topbar">
    <div class="topbar-left">
      <h1 class="page-title">{{ pageTitle }}</h1>
      <div class="breadcrumb">
        <span class="breadcrumb-item">{{ $t('admin.title', 'Admin Panel') }}</span>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-current">{{ currentSection }}</span>
      </div>
    </div>
    
    <div class="topbar-right">
      <div class="admin-stats">
        <span class="stat-label">{{ $t('admin.system_time', 'System Time') }}:</span>
        <span class="stat-value">{{ currentTime }}</span>
      </div>
      
      <button 
        @click="refreshData"
        class="refresh-button"
        :disabled="isRefreshing"
        data-testid="refresh-button"
      >
        <i class="refresh-icon" :class="{ 'spinning': isRefreshing }">🔄</i>
        {{ $t('admin.refresh', 'Refresh') }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminTopbar',
  data() {
    return {
      currentTime: '',
      timeInterval: null,
      isRefreshing: false
    }
  },
  computed: {
    pageTitle() {
      const routeName = this.$route.name
      const titles = {
        'adminDashboard': this.$t('admin.dashboard', 'Dashboard'),
        'adminCompanies': this.$t('admin.companies', 'Companies'),
        'adminSubscriptions': this.$t('admin.subscriptions', 'Subscriptions')
      }
      return titles[routeName] || this.$t('admin.title', 'Admin Panel')
    },
    currentSection() {
      return this.pageTitle
    }
  },
  mounted() {
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeUnmount() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString()
    },
    async refreshData() {
      this.isRefreshing = true
      try {
        // Emit refresh event to parent components
        this.$emit('refresh-data')
        
        // Add a small delay to show the spinning animation
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Reload current route component
        await this.$router.go(0)
      } catch (error) {
        console.error('Failed to refresh data:', error)
      } finally {
        this.isRefreshing = false
      }
    }
  }
}
</script>

<style scoped>
.admin-topbar {
  @apply flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200 shadow-sm;
}

.topbar-left {
  @apply flex flex-col;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.breadcrumb {
  @apply flex items-center text-sm text-gray-500;
}

.breadcrumb-item {
  @apply text-gray-400;
}

.breadcrumb-separator {
  @apply mx-2;
}

.breadcrumb-current {
  @apply text-gray-700 font-medium;
}

.topbar-right {
  @apply flex items-center space-x-4;
}

.admin-stats {
  @apply flex flex-col items-end text-sm;
}

.stat-label {
  @apply text-gray-500 font-medium;
}

.stat-value {
  @apply text-gray-900 font-mono;
}

.refresh-button {
  @apply flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.refresh-icon {
  @apply mr-2 text-lg;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .admin-topbar {
    @apply flex-col items-start space-y-4;
  }
  
  .topbar-right {
    @apply w-full justify-between;
  }
  
  .admin-stats {
    @apply items-start;
  }
}
</style>