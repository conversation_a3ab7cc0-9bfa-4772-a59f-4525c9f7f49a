<!-- ABOUTME: AdminSidebar provides navigation for admin interface with secure route handling -->
<!-- ABOUTME: Displays admin-specific navigation links and user information -->
<template>
  <div class="admin-sidebar" data-testid="admin-sidebar">
    <div class="admin-sidebar-header">
      <h2 class="admin-title">{{ $t('admin.title', 'Admin Panel') }}</h2>
      <div class="admin-user-info">
        <span class="admin-email">{{ userEmail }}</span>
        <span class="admin-role">{{ $t('admin.administrator', 'Administrator') }}</span>
      </div>
    </div>
    
    <nav class="admin-nav">
      <router-link 
        :to="`/${$route.params.locale}/admin/dashboard`"
        class="admin-nav-item"
        active-class="active"
        data-testid="admin-dashboard-link"
      >
        <i class="admin-icon">📊</i>
        {{ $t('admin.dashboard', 'Dashboard') }}
      </router-link>
      
      <router-link 
        :to="`/${$route.params.locale}/admin/companies`"
        class="admin-nav-item"
        active-class="active"
        data-testid="admin-companies-link"
      >
        <i class="admin-icon">🏢</i>
        {{ $t('admin.companies', 'Companies') }}
      </router-link>
      
      <router-link 
        :to="`/${$route.params.locale}/admin/subscriptions`"
        class="admin-nav-item"
        active-class="active"
        data-testid="admin-subscriptions-link"
      >
        <i class="admin-icon">💳</i>
        {{ $t('admin.subscriptions', 'Subscriptions') }}
      </router-link>
    </nav>
    
    <div class="admin-sidebar-footer">
      <router-link 
        :to="`/${$route.params.locale}/dashboard`"
        class="exit-admin-link"
        data-testid="exit-admin-link"
      >
        <i class="admin-icon">↩️</i>
        {{ $t('admin.exit', 'Exit Admin') }}
      </router-link>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AdminSidebar',
  computed: {
    ...mapState('userStore', ['jwtUser', 'user']),
    userEmail() {
      return this.jwtUser?.email || this.user?.email || ''
    }
  }
}
</script>

<style scoped>
.admin-sidebar {
  @apply fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-30;
  background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 100%);
}

.admin-sidebar-header {
  @apply p-6 border-b border-purple-300;
}

.admin-title {
  @apply text-xl font-bold text-white mb-2;
}

.admin-user-info {
  @apply flex flex-col;
}

.admin-email {
  @apply text-purple-100 text-sm;
}

.admin-role {
  @apply text-purple-200 text-xs font-medium;
}

.admin-nav {
  @apply flex flex-col py-4;
}

.admin-nav-item {
  @apply flex items-center px-6 py-3 text-purple-100 hover:bg-purple-600 hover:text-white transition-colors duration-200;
  text-decoration: none;
}

.admin-nav-item.active {
  @apply bg-purple-600 text-white border-r-4 border-purple-200;
}

.admin-icon {
  @apply mr-3 text-lg;
}

.admin-sidebar-footer {
  @apply absolute bottom-0 left-0 right-0 p-6 border-t border-purple-300;
}

.exit-admin-link {
  @apply flex items-center text-purple-200 hover:text-white transition-colors duration-200;
  text-decoration: none;
}

@media (max-width: 1024px) {
  .admin-sidebar {
    @apply w-16;
  }
  
  .admin-title,
  .admin-user-info,
  .admin-nav-item span,
  .exit-admin-link span {
    @apply hidden;
  }
  
  .admin-nav-item,
  .exit-admin-link {
    @apply justify-center;
  }
  
  .admin-icon {
    @apply mr-0;
  }
}
</style>