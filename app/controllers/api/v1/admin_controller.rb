# ABOUTME: AdminController provides API endpoints for admin interface functionality
# ABOUTME: Implements secure company and subscription management with multi-tenant awareness
module Api
  module V1
    class AdminController < Api::V1::ApiController
      before_action :ensure_admin_access
      
      # GET /api/v1/admin/companies
      def companies
        authorize_admin!(:view_all_companies?)
        
        companies = Company.includes(:users, :current_subscription, :current_plan)
                          .with_tenant_disabled do
          Company.all.map do |company|
            owner = company.users_with_role('owner').first
            {
              id: company.id,
              name: company.name,
              created_at: company.created_at,
              owner: owner ? {
                id: owner.id,
                email: owner.email,
                name: "#{owner.user_profile&.first_name} #{owner.user_profile&.last_name}".strip
              } : nil,
              employee_count: company.contracts.count,
              subscription: company.current_subscription&.as_json(
                only: [:id, :status, :trial_end, :current_period_start, :current_period_end]
              ),
              plan: company.current_plan&.as_json(
                only: [:id, :name, :price_cents, :interval]
              )
            }
          end
        end
        
        render json: { companies: companies }
      end
      
      # GET /api/v1/admin/dashboard
      def dashboard
        authorize_admin!(:access?)
        
        stats = Company.with_tenant_disabled do
          {
            total_companies: Company.count,
            active_subscriptions: Subscription.where(status: 'active').count,
            trial_subscriptions: Subscription.where(status: 'trialing').count,
            total_users: User.count,
            total_contracts: Contract.count
          }
        end
        
        render json: { dashboard: stats }
      end
      
      private
      
      def ensure_admin_access
        unless current_user&.admin_user?
          render json: { 
            error: 'Access denied',
            message: 'Admin access required'
          }, status: :forbidden
        end
      end
      
      def authorize_admin!(permission)
        admin_policy = AdminPolicy.new(current_user, :admin)
        unless admin_policy.public_send(permission)
          render json: { 
            error: 'Access denied',
            message: 'Insufficient admin permissions'
          }, status: :forbidden
        end
      end
    end
  end
end