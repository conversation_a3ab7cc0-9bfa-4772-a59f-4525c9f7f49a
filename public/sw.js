// ABOUTME: PWA Service Worker - Phase 1 & 2 implementation with JWT support and offline capabilities
// ABOUTME: Handles service worker registration, caching strategies, and offline API request queuing

const CACHE_NAME = 'tymbox-v1';
const STATIC_CACHE_NAME = 'tymbox-static-v1';
const API_CACHE_NAME = 'tymbox-api-v1';

// Assets to cache for offline use
// TYM-83 FIX: Remove '/' as it may fail in development
const STATIC_ASSETS = [
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// SECURITY FIX (TYM-78): Removed API endpoint caching to prevent sensitive data exposure
// All API requests now use network-only strategy for security

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Install event');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached');
        // Skip waiting removed - let new SW wait for all clients to close
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activate event');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // SECURITY FIX (TYM-85): Delete all caches except current static cache
            // API cache is no longer used (network-only strategy) so delete old API caches
            if (cacheName !== STATIC_CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Cache cleanup complete');
        // TYM-83 FIX: Remove clients.claim() to prevent reload loops
        // Service worker will take control on next navigation/reload naturally
        // return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // TYM-83 FIX: Do NOT intercept API requests - let axios interceptor handle authentication
  // This allows token refresh mechanism to work properly for forever login
  // API requests will use network-only strategy naturally through axios
  
  // Handle static assets only
  if (request.destination === 'document' || 
      request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }
  
  // Default: try network first (for non-API, non-static requests)
  event.respondWith(
    fetch(request).catch(() => {
      return caches.match('/offline.html');
    })
  );
});

// TYM-83 FIX: API request handler no longer actively used
// Keeping function for reference - API requests now handled directly by axios
// This preserves network-only strategy while allowing authentication flow to work
async function handleApiRequest(request) {
  try {
    // SECURITY FIX (TYM-81): Remove client-side JWT validation to prevent token forgery
    // SECURITY FIX (TYM-78): Network-only strategy for API requests to prevent caching sensitive data
    // TYM-83 FIX: Function preserved but not intercepting - axios handles API requests
    
    // Always go to network for API requests - never serve from cache
    // This prevents sensitive user data from being cached in the browser
    const networkResponse = await fetch(request.clone());
    
    // SECURITY: Do NOT cache API responses to prevent sensitive data exposure
    // All API responses contain user-specific or sensitive data that should not be cached
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] Network failed for API request:', error);
    
    // SECURITY: Do NOT serve API requests from cache as fallback
    // API responses often contain sensitive data that becomes stale quickly
    // Better to fail gracefully than serve potentially sensitive cached data
    
    // Queue request for later if offline
    await queueOfflineRequest(request);
    
    return new Response(JSON.stringify({ 
      error: 'Network unavailable', 
      queued: true 
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle static asset requests
async function handleStaticRequest(request) {
  try {
    // Try cache first for static assets
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Try network
    const networkResponse = await fetch(request);
    
    // Cache the response
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request.clone(), networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] Failed to fetch static asset:', error);
    
    // Try cache again
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for document requests
    if (request.destination === 'document') {
      return caches.match('/offline.html');
    }
    
    return new Response('Network Error', { status: 503 });
  }
}

// SECURITY FIX (TYM-81): Removed isValidJWT() function 
// Client-side JWT validation creates token forgery vulnerability
// JWT validation must ONLY happen on the backend server for security

// SECURITY FIX (TYM-78): Removed isCacheableApiEndpoint function
// All API requests now use network-only strategy - no caching for security

// Queue offline requests for background sync
async function queueOfflineRequest(request) {
  try {
    // Simple offline queue using IndexedDB would go here
    // For Phase 2, we'll implement a basic approach
    console.log('[SW] Queuing offline request:', request.url);
    
    // Store request details in IndexedDB or localStorage
    // SECURITY FIX (TYM-85): Strip sensitive headers before storing
    const headers = Object.fromEntries(request.headers.entries());
    // Remove authorization headers to prevent JWT token exposure (case-insensitive)
    Object.keys(headers).forEach(key => {
      if (key.toLowerCase() === 'authorization') {
        delete headers[key];
      }
    });
    
    const requestData = {
      url: request.url,
      method: request.method,
      headers: headers,
      body: await request.text(),
      timestamp: Date.now()
    };
    
    // In a real implementation, store in IndexedDB
    // For now, just log it
    console.log('[SW] Request queued:', requestData);
    
  } catch (error) {
    console.error('[SW] Failed to queue offline request:', error);
  }
}

// Message handling for client communication
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

console.log('[SW] Service Worker loaded successfully');